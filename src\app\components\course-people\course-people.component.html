<div class="relative">
  <!-- Header with search -->
  <div class="flex justify-between items-center mb-4">
    <div class="text-lg font-medium text-gray-800 dark:text-gray-200">
      Участники группы
    </div>
    <div class="relative">
      <input
        type="text"
        [(ngModel)]="searchQuery"
        (input)="filterMembers()"
        placeholder="Поиск участников..."
        class="pl-8 pr-4 py-2 text-xs rounded-lg border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-200"
      />
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 absolute left-2.5 top-2.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </div>
  </div>

  <!-- Role filter chips -->
  <div class="flex flex-wrap gap-2 mb-4">
    <button
      (click)="filterByRole('all')"
      class="px-3 py-1 text-xs rounded-full transition-all"
      [ngClass]="{'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': selectedRole === 'all',
                 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300': selectedRole !== 'all'}">
      Все
    </button>
    <button
      (click)="filterByRole('teacher')"
      class="px-3 py-1 text-xs rounded-full transition-all"
      [ngClass]="{'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': selectedRole === 'teacher',
                 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300': selectedRole !== 'teacher'}">
      Преподаватели
    </button>
    <button
      (click)="filterByRole('student')"
      class="px-3 py-1 text-xs rounded-full transition-all"
      [ngClass]="{'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': selectedRole === 'student',
                 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300': selectedRole !== 'student'}">
      Студенты
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div class="spinner rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
  </div>

  <!-- No results message -->
  <div *ngIf="!isLoading && filteredMembers.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
    </svg>
    <p *ngIf="members.length === 0 && searchQuery === '' && selectedRole === 'all'">В этой группе пока нет участников</p>
    <p *ngIf="members.length > 0 || searchQuery !== '' || selectedRole !== 'all'">Участники не найдены</p>
  </div>

  <!-- Members table -->
  <div *ngIf="!isLoading && filteredMembers.length > 0" class="overflow-x-auto rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
      <thead class="bg-gray-50 dark:bg-gray-800">
        <tr>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Пользователь
          </th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Email
          </th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            ID
          </th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Роль
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
        <tr *ngFor="let member of filteredMembers" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          <td class="px-4 py-3 whitespace-nowrap">
            <div class="flex items-center">
              <!-- Avatar with initials -->
              <div class="flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center text-sm font-medium"
                  [ngClass]="{
                    'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300': member.role === 'teacher',
                    'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300': member.role === 'student',
                    'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300': member.role === 'admin'
                  }">
                {{ getInitials(member.name) }}
              </div>
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ member.name }}
                </div>
              </div>
            </div>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
            {{ member.email }}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
            {{ member.id }}
          </td>
          <td class="px-4 py-3 whitespace-nowrap">
            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                [ngClass]="{
                  'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': member.role === 'teacher',
                  'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': member.role === 'student',
                  'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200': member.role === 'admin'
                }">
              {{ member.role }}
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
