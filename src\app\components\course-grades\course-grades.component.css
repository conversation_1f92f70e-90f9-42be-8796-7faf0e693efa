/* Loading spinner animation */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Sticky columns styling */
.sticky {
  position: sticky;
  z-index: 10;
}

/* Table scroll styling */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Dark mode scrollbar */
.dark .overflow-x-auto {
  scrollbar-color: #4a5568 #2d3748;
}

.dark .overflow-x-auto::-webkit-scrollbar-track {
  background: #2d3748;
}

.dark .overflow-x-auto::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.dark .overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Grade input styling */
input[type="number"] {
  -moz-appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Hover effects for interactive elements */
button:hover svg {
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}

/* Grade badge animations */
.inline-flex {
  transition: all 0.2s ease-in-out;
}

.inline-flex:hover {
  transform: scale(1.05);
}

/* Table row hover effect */
tbody tr:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark tbody tr:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}