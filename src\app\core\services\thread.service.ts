import { Injectable } from '@angular/core';
import {environments} from "../../environments/environments";
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {Router} from "@angular/router";
import {StorageService} from "./storage.service";
import {BehaviorSubject, Observable} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ThreadService {
  private apiUrl = environments.API
  private threadIDSubject = new BehaviorSubject<number | null>(null);
  threadID$ = this.threadIDSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private storage: StorageService
  ) {}


  setThreadID(id: number) {
    this.threadIDSubject.next(id);
  }

  getThreadID(): number | null {
    return this.threadIDSubject.getValue();
  }


  getAllThreads(): Observable<any> {
    return this.http.get(`${this.apiUrl}/thread`);
  }

  getThreadInfoByID(id:number): Observable<any> {
    return this.http.get(`${this.apiUrl}/thread/${id}`);
  }

  registerOnThread(user_id: number, thread_id: number) {
    return this.http.post<{ message: string } | { error: string }>(
      `${this.apiUrl}/thread/register`,
      { user_id, thread_id }
    );
  }


  removeFromThread(user_id: number, thread_id: number) {
    return this.http.post<{ message: string } | { error: string }>(
      `${this.apiUrl}/thread/remove`,
      { user_id, thread_id }
    );
  }

  getListOfThreadsForUser(id: number) {
    return this.http.get(`${this.apiUrl}/thread/user/${id}`);
  }

  /**
   * Get list of threads for a user with their schedules
   * @param id User ID
   * @returns Observable with threads and their schedules
   */
  getThreadsWithScheduleForUser(id: number) {
    return this.http.get(`${this.apiUrl}/thread/user/${id}/with-schedules`);
  }


  getThreadMembers(thread_id: number) {
    return this.http.get(`${this.apiUrl}/thread/members/${thread_id}`);
  }

  /**
   * Get availability information for multiple threads
   * @param threadIds Array of thread IDs to check availability for
   * @returns Observable with thread availability information
   */
  getThreadsAvailability(threadIds: number[]) {
    return this.http.post(`${this.apiUrl}/thread/availability`, { thread_ids: threadIds });
  }

  /**
   * Get list of threads for a teacher
   * @returns Observable with teacher's threads
   */
  getTeacherThreads() {
    const token = this.storage.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.get(`${this.apiUrl}/teacher/my-threads`, { headers });
  }

  /**
   * Trigger availability update on the server
   * This endpoint updates thread availability data on the server side
   * @returns Observable with the response from the availability update
   */
  updateAvailability() {
    return this.http.get(`${this.apiUrl}/thread/availability`);
  }

  /**
   * Get available threads for a specific student
   * @param student_id The ID of the student
   * @returns Observable with available threads for the student
   */
  getAvailableThreadsForStudent(student_id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/students/${student_id}/available-threads`);
  }

  /**
   * Get attendance sessions for a specific thread
   * @param thread_id The ID of the thread
   * @returns Observable with attendance sessions
   */
  getAttendanceSessions(thread_id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/thread/${thread_id}/attendance-sessions`);
  }

  /**
   * Set final grade for a student in a thread
   * @param user_id The ID of the student
   * @param thread_id The ID of the thread
   * @param final_grade The final grade to assign
   * @returns Observable with the response
   */
  setFinalGrade(user_id: number, thread_id: number, final_grade: number): Observable<any> {
    const token = this.storage.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

    return this.http.post(`${this.apiUrl}/thread/grade-final`, {
      user_id,
      thread_id,
      final_grade
    }, { headers });
  }

  /**
   * Get students with their assignment grades and final grades for a thread
   * @param thread_id The ID of the thread
   * @returns Observable with students and their grades
   */
  getStudentGrades(thread_id: number): Observable<any> {
    const token = this.storage.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

    return this.http.get(`${this.apiUrl}/thread/${thread_id}/student-grades`, { headers });
  }

}
