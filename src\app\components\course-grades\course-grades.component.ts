import { Component, Input, OnInit } from '@angular/core';
import { ThreadService } from '../../core/services/thread.service';
import { AssignmentService } from '../../core/services/assignment.service';
import { StorageService } from '../../core/services/storage.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-course-grades',
  templateUrl: './course-grades.component.html',
  styleUrl: './course-grades.component.css'
})
export class CourseGradesComponent implements OnInit {
  @Input() threadID: any;

  students: any[] = [];
  assignments: any[] = [];
  assignmentGroups: any[] = [];
  isLoading: boolean = false;
  isTeacher: boolean = false;
  editingGrades: { [key: string]: boolean } = {};
  tempGrades: { [key: string]: number } = {};

  constructor(
    private threadService: ThreadService,
    private assignmentService: AssignmentService,
    private storageService: StorageService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.checkUserRole();
    if (this.threadID) {
      this.loadGradesData();
    }
  }

  checkUserRole() {
    const user = this.storageService.getUser();
    this.isTeacher = user && (user.role === 'teacher' || user.role === 'admin');
  }

  loadGradesData() {
    this.isLoading = true;

    // Load gradebook data (assignments and students with grades)
    this.assignmentService.getGradebook(this.threadID).subscribe({
      next: (data: any) => {
        this.assignments = data.assignments || [];
        this.students = data.students || [];

        console.log('Gradebook data:', data);
        console.log('Assignments:', this.assignments);
        console.log('Students:', this.students);

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading gradebook:', error);
        // Fallback to loading just members if gradebook endpoint doesn't exist
        this.loadMembersAndAssignments();
      }
    });
  }

  loadMembersAndAssignments() {
    // Fallback method if gradebook endpoint doesn't exist
    this.threadService.getThreadMembers(this.threadID).subscribe({
      next: (data: any) => {
        const members = data.members || [];
        this.students = members.filter((member: any) => member.role === 'student').map((student: any) => ({
          ...student,
          grades: [],
          final_grade: null
        }));

        // Also try to load assignments separately
        this.assignmentService.getListAssignmentGroupForThread(this.threadID).subscribe({
          next: (assignmentData: any) => {
            this.assignmentGroups = assignmentData.assignment_groups || [];
            this.assignments = [];

            // Flatten all assignments from all groups
            this.assignmentGroups.forEach(group => {
              if (group.assignments) {
                this.assignments.push(...group.assignments.map((assignment: any) => ({
                  ...assignment,
                  group_name: group.name,
                  group_type: group.group_type
                })));
              }
            });

            this.isLoading = false;
          },
          error: (error) => {
            console.error('Error loading assignments:', error);
            this.assignments = [];
            this.isLoading = false;
          }
        });
      },
      error: (error) => {
        console.error('Error loading members:', error);
        this.students = [];
        this.assignments = [];
        this.isLoading = false;
      }
    });
  }

  startEditingGrade(studentId: number) {
    const key = `${studentId}`;
    this.editingGrades[key] = true;
    const currentGrade = this.getFinalGrade(studentId);
    const minGrade = this.getMinFinalGrade(studentId);

    // Set the temp grade to at least the minimum required grade
    this.tempGrades[key] = Math.max(currentGrade, minGrade);
  }

  cancelEditingGrade(studentId: number) {
    const key = `${studentId}`;
    this.editingGrades[key] = false;
    delete this.tempGrades[key];
  }

  saveFinalGrade(studentId: number) {
    const key = `${studentId}`;
    const finalGrade = this.tempGrades[key];
    const minGrade = this.getMinFinalGrade(studentId);
    const maxGrade = 100;

    if (finalGrade === undefined || finalGrade < minGrade || finalGrade > maxGrade) {
      this.snackBar.open(`Оценка должна быть от ${minGrade} до ${maxGrade}`, 'Закрыть', {
        duration: 4000,
        panelClass: ['snackbar-error']
      });
      return;
    }

    this.threadService.setFinalGrade(studentId, this.threadID, finalGrade).subscribe({
      next: () => {
        // Update the student's final grade in the local data
        const student = this.students.find(s => s.id === studentId);
        if (student) {
          student.final_grade = finalGrade;
        }

        this.editingGrades[key] = false;
        delete this.tempGrades[key];

        this.snackBar.open('Итоговая оценка сохранена', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-success']
        });
      },
      error: (error) => {
        console.error('Error saving final grade:', error);
        this.snackBar.open('Ошибка при сохранении оценки', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-error']
        });
      }
    });
  }

  getFinalGrade(studentId: number): number {
    const student = this.students.find(s => s.id === studentId);
    return student?.final_grade || 0;
  }

  getAssignmentGrade(studentId: number, assignmentId: number): string {
    const student = this.students.find(s => s.id === studentId);
    if (student && student.grades) {
      const grade = student.grades.find((g: any) => g.assignment_id === assignmentId);
      return grade && grade.score !== undefined && grade.score !== null ? grade.score.toString() : '-';
    }
    return '-';
  }

  getInitials(student: any): string {
    if (!student) return '?';

    const firstName = student.name || '';
    const lastName = student.surname || '';

    if (firstName && lastName) {
      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
    } else if (firstName) {
      return firstName.charAt(0).toUpperCase();
    } else if (lastName) {
      return lastName.charAt(0).toUpperCase();
    }

    return '?';
  }

  onTempGradeChange(studentId: number, event: any) {
    const key = `${studentId}`;
    this.tempGrades[key] = parseFloat(event.target.value) || 0;
  }

  getAssignmentGradeNumber(studentId: number, assignmentId: number): number {
    const gradeStr = this.getAssignmentGrade(studentId, assignmentId);
    return gradeStr === '-' ? -1 : parseFloat(gradeStr);
  }

  isGradeExcellent(grade: string): boolean {
    if (grade === '-') return false;
    const numGrade = parseFloat(grade);
    return numGrade >= 70;
  }

  isGradeGood(grade: string): boolean {
    if (grade === '-') return false;
    const numGrade = parseFloat(grade);
    return numGrade >= 50 && numGrade < 70;
  }

  isGradePoor(grade: string): boolean {
    if (grade === '-') return false;
    const numGrade = parseFloat(grade);
    return numGrade < 50;
  }

  /**
   * Calculate the total score of all assignments for a student
   */
  getTotalAssignmentScore(studentId: number): number {
    const student = this.students.find(s => s.id === studentId);
    if (!student || !student.grades) return 0;

    let total = 0;
    student.grades.forEach((grade: any) => {
      if (grade.score !== undefined && grade.score !== null) {
        total += parseFloat(grade.score);
      }
    });

    return total;
  }

  /**
   * Get the minimum allowed final grade (sum of all assignment scores)
   */
  getMinFinalGrade(studentId: number): number {
    return this.getTotalAssignmentScore(studentId);
  }

  /**
   * Get the maximum possible score for all assignments
   */
  getMaxPossibleScore(): number {
    return this.assignments.reduce((total, assignment) => {
      return total + (assignment.max_points || 100);
    }, 0);
  }
}
