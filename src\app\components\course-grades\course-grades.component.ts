import { Component, Input, OnInit } from '@angular/core';
import { ThreadService } from '../../core/services/thread.service';
import { AssignmentService } from '../../core/services/assignment.service';
import { StorageService } from '../../core/services/storage.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-course-grades',
  templateUrl: './course-grades.component.html',
  styleUrl: './course-grades.component.css'
})
export class CourseGradesComponent implements OnInit {
  @Input() threadID: any;

  students: any[] = [];
  assignments: any[] = [];
  assignmentGroups: any[] = [];
  isLoading: boolean = false;
  isTeacher: boolean = false;
  editingGrades: { [key: string]: boolean } = {};
  tempGrades: { [key: string]: number } = {};

  constructor(
    private threadService: ThreadService,
    private assignmentService: AssignmentService,
    private storageService: StorageService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.checkUserRole();
    if (this.threadID) {
      this.loadGradesData();
    }
  }

  checkUserRole() {
    const user = this.storageService.getUser();
    this.isTeacher = user && (user.role === 'teacher' || user.role === 'admin');
  }

  loadGradesData() {
    this.isLoading = true;

    // Load students and their grades
    this.threadService.getStudentGrades(this.threadID).subscribe({
      next: (data: any) => {
        this.students = data.students || [];
        console.log('Students with grades:', this.students);
      },
      error: (error) => {
        console.error('Error loading student grades:', error);
        // Fallback to loading just members if grades endpoint doesn't exist
        this.loadMembersAndAssignments();
      }
    });

    // Load assignment groups and assignments
    this.assignmentService.getListAssignmentGroupForThread(this.threadID).subscribe({
      next: (data: any) => {
        this.assignmentGroups = data.assignment_groups || [];
        this.assignments = [];

        // Flatten all assignments from all groups
        this.assignmentGroups.forEach(group => {
          if (group.assignments) {
            this.assignments.push(...group.assignments.map((assignment: any) => ({
              ...assignment,
              group_name: group.name,
              group_type: group.group_type
            })));
          }
        });

        console.log('Assignment groups:', this.assignmentGroups);
        console.log('All assignments:', this.assignments);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading assignments:', error);
        this.isLoading = false;
      }
    });
  }

  loadMembersAndAssignments() {
    // Fallback method if student grades endpoint doesn't exist
    this.threadService.getThreadMembers(this.threadID).subscribe({
      next: (data: any) => {
        const members = data.members || [];
        this.students = members.filter((member: any) => member.role === 'student');
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading members:', error);
        this.isLoading = false;
      }
    });
  }

  startEditingGrade(studentId: number) {
    const key = `${studentId}`;
    this.editingGrades[key] = true;
    this.tempGrades[key] = this.getFinalGrade(studentId);
  }

  cancelEditingGrade(studentId: number) {
    const key = `${studentId}`;
    this.editingGrades[key] = false;
    delete this.tempGrades[key];
  }

  saveFinalGrade(studentId: number) {
    const key = `${studentId}`;
    const finalGrade = this.tempGrades[key];

    if (finalGrade === undefined || finalGrade < 0 || finalGrade > 100) {
      this.snackBar.open('Оценка должна быть от 0 до 100', 'Закрыть', {
        duration: 3000,
        panelClass: ['snackbar-error']
      });
      return;
    }

    this.threadService.setFinalGrade(studentId, this.threadID, finalGrade).subscribe({
      next: () => {
        // Update the student's final grade in the local data
        const student = this.students.find(s => s.id === studentId);
        if (student) {
          student.final_grade = finalGrade;
        }

        this.editingGrades[key] = false;
        delete this.tempGrades[key];

        this.snackBar.open('Итоговая оценка сохранена', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-success']
        });
      },
      error: (error) => {
        console.error('Error saving final grade:', error);
        this.snackBar.open('Ошибка при сохранении оценки', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-error']
        });
      }
    });
  }

  getFinalGrade(studentId: number): number {
    const student = this.students.find(s => s.id === studentId);
    return student?.final_grade || 0;
  }

  getAssignmentGrade(studentId: number, assignmentId: number): string {
    const student = this.students.find(s => s.id === studentId);
    if (student && student.assignment_grades) {
      const grade = student.assignment_grades.find((g: any) => g.assignment_id === assignmentId);
      return grade ? grade.score.toString() : '-';
    }
    return '-';
  }

  getInitials(name: string): string {
    if (!name) return '?';
    const parts = name.split(' ');
    if (parts.length === 1) {
      return name.charAt(0).toUpperCase();
    }
    return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
  }

  onTempGradeChange(studentId: number, event: any) {
    const key = `${studentId}`;
    this.tempGrades[key] = parseFloat(event.target.value) || 0;
  }

  getAssignmentGradeNumber(studentId: number, assignmentId: number): number {
    const gradeStr = this.getAssignmentGrade(studentId, assignmentId);
    return gradeStr === '-' ? -1 : parseFloat(gradeStr);
  }

  isGradeExcellent(grade: string): boolean {
    if (grade === '-') return false;
    const numGrade = parseFloat(grade);
    return numGrade >= 70;
  }

  isGradeGood(grade: string): boolean {
    if (grade === '-') return false;
    const numGrade = parseFloat(grade);
    return numGrade >= 50 && numGrade < 70;
  }

  isGradePoor(grade: string): boolean {
    if (grade === '-') return false;
    const numGrade = parseFloat(grade);
    return numGrade < 50;
  }
}
