import {Component, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, HostListener, AfterViewInit} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {CourseService} from "../../core/services/course.service";
import {StorageService} from "../../core/services/storage.service";
import {ThreadService} from "../../core/services/thread.service";
import {WeekAddDialogComponent} from "../teacher/week-add-dialog/week-add-dialog.component";
import {WeekService} from "../../core/services/week.service";
import {MatSnackBar} from "@angular/material/snack-bar";
import {MatDialog} from "@angular/material/dialog";
import {CourseWeeksComponent} from "../course-weeks/course-weeks.component";
import {AuthService} from "../../core/services/auth.service";
import {forkJoin, of} from 'rxjs';
import {catchError, map} from 'rxjs/operators';


@Component({
  selector: 'app-course-detail',
  templateUrl: './course-detail.component.html',
  styleUrl: './course-detail.component.css'
})
export class CourseDetailComponent implements OnInit, AfterViewInit {
  @ViewChild(CourseWeeksComponent) courseWeeksComponent!: CourseWeeksComponent;
  @ViewChild('sidebarContainer') sidebarContainer!: ElementRef;

  constructor(private route: ActivatedRoute,
              private router: Router,
              private courseService: CourseService,
              private storageService: StorageService,
              private threadService: ThreadService,
              private weekService: WeekService,
              private snackBar: MatSnackBar,
              private dialog: MatDialog,
              private authService: AuthService) {
    // Initialize user from auth service
    this.user = authService.getCurrentUser();
  }

  breadcrumbs: { label: string, url?: string }[] = [];

  menuSections = [
    {key: 'general', title: 'MENU.General', icon: '/assets/icons/dashboard.svg'},
    // {key: 'program', title: 'MENU.Program', icon: '/assets/icons/learn.svg'},
    {key: 'grades', title: 'MENU.Grades', icon: '/assets/icons/ratings.svg'},
    {key: 'people', title: 'MENU.People', icon: '/assets/icons/people.svg'},
  ];

  teacherMenuSections = [
    // {key: 'set-attendance', title: 'Отметить Посещяемость', icon: '/assets/icons/info.svg'},
    // {key: 'add-task', title: 'Добавить лекцию/задание', icon: '/assets/icons/info.svg'},
  ];

  threadID: number | null = null;
  threadInfo: any;
  courseInfo: any;
  user: any;
  course: any;
  value = 0; // стартовое значение для анимации
  finalValue = 0; // оценка, которую нужно отобразить

  // Assignment data
  assignmentGroups: any[] = [];
  assignments: any[] = [];
  totalPoints: number = 0;
  earnedPoints: number = 0;
  assignmentsLoaded: boolean = false;


  @HostListener('window:resize', ['$event'])
  onResize() {
    this.checkScreenSize();
  }

  ngOnInit() {
    this.user = this.storageService.getUser();
    this.checkScreenSize(); // Check initial screen size

    this.route.params.subscribe(params => {
      const id = +params['id']; // (+) converts string 'id' to a number
      console.log('THREAD ID:', this.threadID = id);
      this.threadService.setThreadID(id); // Сохраняем ID
      this.getThreadInfo(id);

      // If user is a student, load assignment data for grade calculation
      if (this.user && this.user.role === 'student' && this.user.id) {
        this.loadAssignmentData(id, this.user.id);
      }
    });

    this.route.queryParams.subscribe(params => {
      this.activeTab = params['tab'] || 'general';
    });
  }

  ngAfterViewInit() {
    // Any initialization that requires the view to be fully rendered
    this.checkScreenSize();
  }

  /**
   * Check if the screen is large (desktop) or small (mobile)
   */
  checkScreenSize() {
    this.isLargeScreen = window.innerWidth >= 1024; // lg breakpoint in Tailwind

    // Auto-expand sidebar on large screens
    if (this.isLargeScreen) {
      this.isSidebarCollapsed = false;
    }
  }


  getThreadInfo(id: number) {
    this.threadService.getThreadInfoByID(id).subscribe({
      next: data => {
        this.threadInfo = data
        this.getCourseInfo(this.threadInfo.course_id)
      }, error: err => {
        console.log(err)
      }
    })
  }


  getCourseInfo(id: number) {
    this.courseService.getCourseByID(id).subscribe({
      next: data => {
        this.courseInfo = data
        console.log(data)

        if (this.courseInfo) {
          this.breadcrumbs = [
            {label: 'Главная', url: '/'},
            {label: 'Курсы', url: '/courses'},
            {label: this.courseInfo.title,},
          ]
        }

      }, error: err => {
        console.log(err)
      }
    })
  }


  openAddWeekDialog() {
    const threadID = this.threadService.getThreadID();

    const dialogRef = this.dialog.open(WeekAddDialogComponent, {
      width: '400px',
      data: threadID
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && threadID !== null) {
        const {week_number, type, title, description} = result;

        this.weekService.createWeek(threadID, week_number, type, title, description).subscribe({
          next: (res) => {
            console.log('Успешно:', res);
            if (this.courseWeeksComponent) {
              this.courseWeeksComponent.loadHomework(threadID, this.user.id);
            }
            this.snackBar.open('Неделя успешна добавлена', 'Закрыть', {
              duration: 6000,
              horizontalPosition: 'center',
              verticalPosition: 'bottom',
              panelClass: ['snackbar-success']
            });
          },
          error: (err) => {
            console.error('Ошибка:', err);
            this.snackBar.open('Ошибка при создании', 'Закрыть', {
              duration: 6000,
              horizontalPosition: 'center',
              verticalPosition: 'bottom',
              panelClass: ['snackbar-success']
            });
          }
        });
      }
    });
  }





  openAddTask() {
    window.location.replace('/add-task')
  }


  activeTab: string = 'general'; // по умолчанию
  isSidebarCollapsed: boolean = false; // For mobile view
  isLargeScreen: boolean = false; // Track screen size

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {tab: tab},
      queryParamsHandling: 'merge',
    });

    // Auto-collapse sidebar on mobile after selecting a tab
    if (!this.isLargeScreen) {
      this.isSidebarCollapsed = true;
    }
  }

  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }


  get progressStyle() {
    return {
      '--value': `${this.value}%`,
      '--color': this.getColor(this.value),
    };
  }

  animateProgress() {
    const stepTime = 7;
    const interval = setInterval(() => {
      if (this.value < this.finalValue) {
        this.value += 1;
      } else {
        clearInterval(interval);
      }
    }, stepTime);
  }

  getColor(val: number): string {
    if (val < 50) return '#ef4444'; // красный
    if (val < 80) return '#facc15'; // жёлтый
    return '#22c55e';              // зелёный
  }

  /**
   * Load assignment data for the current thread and calculate overall grade
   * @param threadId The thread ID
   * @param userId The user ID
   */
  loadAssignmentData(threadId: number, userId: number) {
    this.assignmentsLoaded = false;

    // Use the weekService to get thread homework data which includes assignments
    this.weekService.getThreadHomework(threadId, userId).subscribe({
      next: (data) => {
        if (data && data.weeks && Array.isArray(data.weeks)) {
          this.processAssignmentData(data.weeks);
        } else {
          console.error('Invalid homework data format:', data);
          this.assignmentsLoaded = true;
        }
      },
      error: (err) => {
        console.error('Error loading assignment data:', err);
        this.assignmentsLoaded = true;
      }
    });
  }

  /**
   * Process assignment data and calculate overall grade
   * @param weeksData The weeks data from the API
   */
  processAssignmentData(weeksData: any[]) {
    // Reset counters
    this.totalPoints = 0;
    this.earnedPoints = 0;
    this.assignments = [];

    // Process each week's assignments
    weeksData.forEach(weekData => {
      if (weekData.assignments && Array.isArray(weekData.assignments)) {
        weekData.assignments.forEach((assignmentData: any) => {
          // Only include task-type assignments with max_points in the calculation
          if (assignmentData.assignment &&
              assignmentData.assignment.type === 'task' &&
              assignmentData.assignment.max_points) {

            // Add to the list of assignments
            this.assignments.push(assignmentData);

            // Add to total possible points
            const maxPoints = assignmentData.assignment.max_points || 0;
            this.totalPoints += maxPoints;

            // If there's a submission with a score, add to earned points
            if (assignmentData.submission &&
                assignmentData.submission.score !== undefined &&
                assignmentData.submission.score !== null) {
              this.earnedPoints += assignmentData.submission.score;
            }
          }
        });
      }
    });

    // Calculate the overall percentage
    if (this.totalPoints > 0) {
      this.finalValue = Math.round((this.earnedPoints / this.totalPoints) * 100);
    } else {
      this.finalValue = 0;
    }

    console.log(`Overall grade calculation: ${this.earnedPoints}/${this.totalPoints} = ${this.finalValue}%`);

    // Start the animation
    this.animateProgress();
    this.assignmentsLoaded = true;
  }
}
